{"name": "ocj2-vue2-nuxt2-webpack", "version": "1.0.0", "private": true, "scripts": {"dev": "nuxt", "build": "nuxt build", "start": "nuxt start", "generate": "nuxt generate", "clean": "rm -r .nuxt"}, "dependencies": {"@google/model-viewer": "1.10.0", "core-js": "^3.22.2", "file-loader": "^6.2.0", "nuxt": "^2.15.8", "opencascade.js": "2.0.0-beta.c301f5e", "vue": "^2.6.14", "vue-server-renderer": "^2.6.14", "vue-template-compiler": "^2.6.14", "webpack": "^4.46.0"}}